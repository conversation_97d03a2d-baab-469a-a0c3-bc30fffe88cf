package com.solum.xplain.xva.calculation

import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_1M
import static com.solum.xplain.core.calibration.CurveSample.discountCurve

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.basics.currency.FxRate
import com.opengamma.strata.data.FxRateId
import com.opengamma.strata.data.ImmutableMarketData
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider
import com.opengamma.strata.product.credit.type.CdsQuoteConvention
import com.solum.xplain.core.calibration.CurveSample
import com.solum.xplain.core.common.AuditUserMapperImpl
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveBuilder
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveCdsNodeBuilder
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveFundingNode
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeValueView
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapperImpl
import com.solum.xplain.core.market.mapping.MarketDataUtils
import com.solum.xplain.extensions.enums.CreditDocClause
import com.solum.xplain.extensions.enums.CreditSeniority
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ContextConfiguration(classes = [XvaMapper.class, CurveGroupFxVolatilityMapperImpl.class, AuditUserMapperImpl.class])
class XvaMapperTest extends Specification {
  @Autowired
  XvaMapper xvaMapper
  @SpringBean
  ReferenceData referenceData = ReferenceData.standard()

  def "should create funding discount factors"() {
    setup:
    def valuationDate = LocalDate.parse("2016-01-01")
    def rates = ImmutableRatesProvider
      .builder(valuationDate)
      .discountCurve(Currency.EUR, discountCurve())
      .build()

    when:
    def values = xvaMapper.ofFunding(Currency.EUR, rates, valuationDate.plusMonths(3))

    then:
    values.dates == [valuationDate, valuationDate.plusMonths(1), valuationDate.plusMonths(2)]
    values.dfs[0] == 1
    values.dfs.size() == 3
  }

  def "should create libor discount factors"() {
    setup:
    def valuationDate = LocalDate.parse("2016-01-01")
    def rates = ImmutableRatesProvider.builder(valuationDate)
      .indexCurve(EUR_EURIBOR_1M, CurveSample.indexCurve()).build()
    when:
    def values = xvaMapper.ofIndex(EUR_EURIBOR_1M, rates, valuationDate.plusMonths(3))

    then:
    values.dates == [valuationDate, valuationDate.plusMonths(1), valuationDate.plusMonths(2)]
    values.dfs[0] == 1
    values.dfs.size() == 3
  }

  def "should create credit value"() {
    setup:
    def curve = new CreditCurveBuilder()
      .reference("legal")
      .currency("EUR")
      .seniority(CreditSeniority.SNRFOR)
      .docClause(CreditDocClause.CR)
      .name("legal_EUR_SNRFOR_CR")
      .quoteConvention(CdsQuoteConvention.POINTS_UPFRONT.name())
      .cdsNodes([
        new CreditCurveCdsNodeBuilder()
        .tenor("1Y")
        .build(),
        new CreditCurveCdsNodeBuilder()
        .tenor("2Y")
        .build()
      ])
      .build()
    def marketData = ImmutableMarketData.builder(LocalDate.now())
      .addValue(MarketDataUtils.quoteId("1Y_legal_EUR_SNRFOR_CR_UF"), 1 as double)
      .addValue(MarketDataUtils.quoteId("2Y_legal_EUR_SNRFOR_CR_UF"), 2 as double)
      .build()
    when:
    def value = xvaMapper.creditValueOf(curve, marketData)

    then:
    value.party == "CORP"
    value.recovery == 0.01d
    value.xlabels == ["1Y", "2Y"]
    value.data == [1d, 2d]
  }

  def "should create funding spreads"() {
    setup:
    def curves = [
      new CreditCurveBuilder()
      .currency("EUR")
      .reference("Entity1")
      .corpTicker("CORP1")
      .fundingNodes([new CreditCurveFundingNode(tenor: "1Y")]).build(),
      new CreditCurveBuilder()
      .currency("EUR")
      .reference("Entity2")
      .corpTicker("CORP2")
      .fundingNodes([new CreditCurveFundingNode(tenor: "2Y")]).build(),
      new CreditCurveBuilder()
      .currency("EUR")
      .reference("Entity3")
      .corpTicker("CORP3")
      .fundingNodes([
        new CreditCurveFundingNode(tenor: "1Y"),
        new CreditCurveFundingNode(tenor: "5Y")
      ]).build(),
      new CreditCurveBuilder()
      .currency("EUR")
      .reference("Entity4")
      .corpTicker("CORP4")
      .fundingNodes([]).build()
    ]
    def marketData = ImmutableMarketData.builder(LocalDate.now())
      .addValue(MarketDataUtils.quoteId("1Y_Entity1_EUR_FUNDING"), 1 as double)
      .addValue(MarketDataUtils.quoteId("5Y_Entity3_EUR_FUNDING"), 5 as double)
      .build()

    when:
    def funding = xvaMapper.fundingSpreadsOf(curves, marketData)

    then:
    funding.name == ["CORP1", "CORP2", "CORP3", "CORP4"] as List<String>
    funding.spread == [1, 0, 5, 0] as List<Double>
  }

  def "should create FxSpotValue"() {
    setup:
    def marketData = ImmutableMarketData.builder(LocalDate.now())
      .addValue(FxRateId.of(CurrencyPair.parse("EUR/USD")), FxRate.of(CurrencyPair.parse("EUR/USD"), 0.1))
      .addValue(FxRateId.of(CurrencyPair.parse("GBP/USD")), FxRate.of(CurrencyPair.parse("GBP/USD"), 0.2))
      .addValue(FxRateId.of(CurrencyPair.parse("AUD/USD")), FxRate.of(CurrencyPair.parse("AUD/USD"), 0.3))
      .build()

    when:
    def value = xvaMapper.fxSpotOf(marketData, Currency.EUR)

    then:
    value.symbol.indexOf("AUDUSD") != -1
    value.rate[value.symbol.indexOf("AUDUSD")] == 0.3
    value.symbol.indexOf("GBPUSD") != -1
    value.rate[value.symbol.indexOf("GBPUSD")] == 0.2
    value.symbol.indexOf("USDEUR") != -1
    value.rate[value.symbol.indexOf("USDEUR")] == 10.0
    value.symbol.indexOf("EUREUR") != -1
    value.rate[value.symbol.indexOf("EUREUR")] == 1
  }

  def "should create value"() {
    setup:
    def matrixValues = [
      new VolatilityNodeValueView(expiry: "1Y", tenor: "5Y", value: 1),
      new VolatilityNodeValueView(expiry: "1Y", tenor: "6Y", value: 2),
      new VolatilityNodeValueView(expiry: "2Y", tenor: "5Y", value: 3),
      new VolatilityNodeValueView(expiry: "2Y", tenor: "6Y", value: 4),
    ]
    def surface = new VolatilitySurfaceBuilder().build()

    when:
    def result = xvaMapper.toSwaptionVolatilitiesValue(surface, matrixValues)

    then:
    result.xlabels == ["1Y", "2Y"]
    result.ylabels == ["5Y", "6Y"]
    result.data == [[1, 2], [3, 4]] as double[][]
    result.fixedFrequency == 1
    result.floatingFrequency == 4
    result.fixedDCT == "30/360"
    result.floatingDCT == "A/360"
  }

  def "should map OIS surface"() {
    setup:
    def surface = new VolatilitySurfaceBuilder().build()
    surface.setName("USD SOFR Vols")

    when:
    def result = xvaMapper.toSwaptionVolatilitiesValue(surface, [])

    then:
    result.fixedFrequency == 1
    result.floatingFrequency == 1
    result.fixedDCT == "A/360"
    result.floatingDCT == "A/360"
  }

  def "should create IR basis spreads"() {
    setup:
    def valuationDate = LocalDate.parse("2016-01-01")
    def rates = ImmutableRatesProvider
      .builder(valuationDate)
      .discountCurve(Currency.USD, discountCurve())
      .indexCurve(com.opengamma.strata.basics.index.OvernightIndices.USD_SOFR, discountCurve())
      .indexCurve(com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_3M, discountCurve())
      .build()

    when:
    def result = xvaMapper.irBasisSpreadsOf(Currency.USD, rates)

    then:
    result.tenors == ["6M", "1Y", "2Y", "5Y", "10Y"]
    result.rates.size() == 5
    result.iborFreq == [4, 4, 4, 4, 4] // 3M = quarterly
    result.iborDct == ["Act/360", "Act/360", "Act/360", "Act/360", "Act/360"]
    result.rfrFreq == [1, 1, 1, 1, 1] // Daily
    result.rfrDct == ["Act/360", "Act/360", "Act/360", "Act/360", "Act/360"]
    result.spreadIsOn == ["RFR", "RFR", "RFR", "RFR", "RFR"]
  }
}
