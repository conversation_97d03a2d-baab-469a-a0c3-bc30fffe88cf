package com.solum.xplain.xva.integration.files

import static com.solum.xplain.core.market.mapping.MarketDataUtils.quoteId

import com.google.common.collect.ImmutableList
import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.data.ImmutableMarketData
import com.solum.xplain.core.common.AuditUserMapperImpl
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceNode
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapperImpl
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNodeBuilder
import com.solum.xplain.xva.calculation.XvaMapper
import com.solum.xplain.xva.proxy.messages.CorrelationsValue
import com.solum.xplain.xva.proxy.messages.CreditValue
import com.solum.xplain.xva.proxy.messages.DiscountFactorsValue
import com.solum.xplain.xva.proxy.messages.FxVolValue
import com.solum.xplain.xva.proxy.messages.IRBasisSpreadsValue
import com.solum.xplain.xva.proxy.messages.SwaptionVolatilitiesValue
import com.solum.xplain.xva.proxy.messages.XvaMarketFileBuilder
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ContextConfiguration(classes = [XvaMapper.class, CurveGroupFxVolatilityMapperImpl.class, AuditUserMapperImpl.class])
class XvaMarketFileBuilderTest extends Specification {
  private static VAL_DT = LocalDate.of(2022, 1, 1)

  @Autowired
  XvaMapper xvaMapper
  @SpringBean
  ReferenceData referenceData = ReferenceData.standard()

  def "should add fxVol for currencyPair with changed order"() {
    setup:
    def fxVol = new FxVolValue([], [])

    when:
    def file = new XvaMarketFileBuilder("EUR")
      .fxVolWithSlash("EUR/USD", fxVol)
      .build()

    then:
    file["FxVol_USDEUR"] == fxVol
  }

  def "should add fxVol for currencyPair with same order"() {
    setup:
    def fxVol = new FxVolValue([], [])

    when:
    def file = new XvaMarketFileBuilder("USD")
      .fxVolWithSlash("EUR/USD", fxVol)
      .build()

    then:
    file["FxVol_EURUSD"] == fxVol
  }

  def "should add fxVol for curve group fxVols"() {
    setup:
    def fxVols = [
      new CurveGroupFxVolatilityNodeBuilder()
      .domesticCurrency("EUR")
      .foreignCurrency("USD")
      .expiry("1Y")
      .build(),
      new CurveGroupFxVolatilityNodeBuilder()
      .domesticCurrency("EUR")
      .foreignCurrency("USD")
      .expiry("2Y")
      .build(),
      new CurveGroupFxVolatilityNodeBuilder()
      .domesticCurrency("EUR")
      .foreignCurrency("GBP")
      .expiry("1Y")
      .build(),
      new CurveGroupFxVolatilityNodeBuilder()
      .domesticCurrency("EUR")
      .foreignCurrency("GBP")
      .expiry("2Y")
      .build()
    ]

    def marketData = ImmutableMarketData.builder(VAL_DT)
      .addValue(quoteId("1Y_EUR/USDV"), 1d)
      .addValue(quoteId("2Y_EUR/USDV"), 2d)
      .addValue(quoteId("1Y_EUR/GBPV"), 3d)
      .addValue(quoteId("2Y_EUR/GBPV"), 4d)
      .build()

    when:
    def fileBuilder = new XvaMarketFileBuilder("EUR")
    xvaMapper.fxVol(fxVols, marketData).each { k, v ->
      fileBuilder.fxVolWithSlash(k, new FxVolValue(
        ImmutableList.copyOf(v.keySet()),
        v.values().collect({ it.value })))
    }
    def file = fileBuilder.build()

    then:
    FxVolValue valueUsd = file["FxVol_USDEUR"]
    valueUsd.xlabels == ["1Y", "2Y"]
    valueUsd.data == [1, 2]
    FxVolValue valueGbp = file["FxVol_GBPEUR"]
    valueGbp.xlabels == ["1Y", "2Y"]
    valueGbp.data == [3, 4]
  }

  def "should add correlations"() {
    setup:
    def value = new CorrelationsValue([], [], [][] as Double[][])

    when:
    def file = new XvaMarketFileBuilder("USD")
      .correlations(value)
      .build()

    then:
    file["Correlations_USD"] == value
  }

  def 'should add discountFactorsFloating'() {
    setup:
    def val = new DiscountFactorsValue([], [], null)

    when:
    def file = new XvaMarketFileBuilder("EUR")
      .discountFactorsFloating("EUR", val)
      .build()

    then:
    file["DiscountFactorsFloating_EUR"] == val
  }

  def "should add discountFactorsFunding"() {
    setup:
    def val = new DiscountFactorsValue([], [],null)

    when:
    def file = new XvaMarketFileBuilder("EUR")
      .discountFactorsFunding("USD", val)
      .build()

    then:
    file["DiscountFactorsFunding_USD"] == val
  }

  def "should add swaptionVols"() {
    setup:
    def val = new SwaptionVolatilitiesValue(1, 1, "", "", [], [], [][] as double[][])

    when:
    def file = new XvaMarketFileBuilder("EUR")
      .swaptionVols("USD", val)
      .build()

    then:
    file["SwaptionVols_USD"] == val
  }

  def "should add credit"() {
    setup:
    def credit = new CreditValue("PARTY", 0.01, ["1M", "2M"], [0.05, 0.04])
    when:
    def file = new XvaMarketFileBuilder("EUR")
      .creditValue(credit)
      .build()

    then:
    file["Credit_PARTY"] == credit
  }

  def "should add IR basis spreads"() {
    setup:
    def tenors = ["6M", "1Y", "2Y"]
    def rates = [0.001, 0.002, 0.003]
    def iborFreq = [4, 4, 4]
    def iborDct = ["A/360", "A/360", "A/360"]
    def rfrFreq = [1, 1, 1]
    def rfrDct = ["A/360", "A/360", "A/360"]
    def spreadIsOn = ["RFR", "RFR", "RFR"]
    def irBasisSpreads = new IRBasisSpreadsValue(tenors, rates, iborFreq, iborDct, rfrFreq, rfrDct, spreadIsOn)

    when:
    def file = new XvaMarketFileBuilder("EUR")
      .irBasisSpreads("USD", irBasisSpreads)
      .build()

    then:
    file["IRBasisSpreads_USD"] == irBasisSpreads
  }

  def "should add swaptionVols from curve group surface"() {
    setup:
    def surface = new VolatilitySurfaceBuilder()
      .nodes([
        new VolatilitySurfaceNode(expiry: "1M", tenor: "1M"),
        new VolatilitySurfaceNode(expiry: "2M", tenor: "1M"),
        new VolatilitySurfaceNode(expiry: "1M", tenor: "2M"),
        new VolatilitySurfaceNode(expiry: "2M", tenor: "2M"),
      ]).build()

    def marketData = ImmutableMarketData.builder(VAL_DT)
      .addValue(quoteId("1MV1M_ATM_EUR-EURIBOR-3M"), 0.11d)
      .addValue(quoteId("1MV2M_ATM_EUR-EURIBOR-3M"), 0.12d)
      .addValue(quoteId("2MV1M_ATM_EUR-EURIBOR-3M"), 0.21d)
      .addValue(quoteId("2MV2M_ATM_EUR-EURIBOR-3M"), 0.22d)
      .build()

    when:
    def fileBuilder = new XvaMarketFileBuilder("EUR")
      .swaptionVols(surface.applicableRateIndex().currency.code,
      xvaMapper.swaptionVols(surface, marketData))

    def file = fileBuilder.build()


    then:
    file["SwaptionVols_EUR"]
  }

  def "should throw an error when swap vol matrix incomplete"() {
    setup:
    def surface = new VolatilitySurfaceBuilder().nodes([
      new VolatilitySurfaceNode(expiry: "1M", tenor: "T11"),
      new VolatilitySurfaceNode(expiry: "2M", tenor: "T12"),
      new VolatilitySurfaceNode(expiry: "1M", tenor: "T21"),
    ]).build()

    def marketData = ImmutableMarketData.builder(VAL_DT)
      .addValue(quoteId("T11"), 0.11d)
      .addValue(quoteId("T12"), 0.12d)
      .addValue(quoteId("T21"), 0.21d)
      .addValue(quoteId("T22"), 0.22d)
      .build()

    when:
    new XvaMarketFileBuilder("EUR")
      .swaptionVols(surface.applicableRateIndex().currency.code,
      xvaMapper.swaptionVols(surface, marketData))

    then:
    thrown IllegalArgumentException
  }

  def "should throw an error when swap vol market data is not available"() {
    setup:
    def surface = new VolatilitySurfaceBuilder().nodes([
      new VolatilitySurfaceNode(expiry: "1M", tenor: "T11"),
      new VolatilitySurfaceNode(expiry: "2M", tenor: "T12"),
      new VolatilitySurfaceNode(expiry: "1M", tenor: "T21"),
      new VolatilitySurfaceNode(expiry: "2M", tenor: "T22"),
    ]).build()

    when:
    new XvaMarketFileBuilder("EUR")
      .swaptionVols(surface.applicableRateIndex().currency.code,
      xvaMapper.swaptionVols(surface, ImmutableMarketData.builder(VAL_DT).build()))

    then:
    thrown IllegalArgumentException
  }
}
